<?php
/**
 * Test getById method
 */

echo "Testing getById method...\n\n";

try {
    require_once 'src/models/ResearchSession.php';
    
    $sessionModel = new ResearchSession();
    echo "✅ ResearchSession model loaded\n";
    
    // Test getById method
    $session = $sessionModel->getById(1);
    
    if ($session) {
        echo "✅ getById(1) returned data:\n";
        echo "   ID: " . $session['id'] . "\n";
        echo "   Title: " . $session['title'] . "\n";
        echo "   Status: " . $session['status'] . "\n";
    } else {
        echo "❌ getById(1) returned no data\n";
    }
    
    // Test getSessionWithDetails method
    echo "\nTesting getSessionWithDetails...\n";
    $sessionDetails = $sessionModel->getSessionWithDetails(1, 1);
    
    if ($sessionDetails) {
        echo "✅ getSessionWithDetails(1, 1) returned data:\n";
        echo "   ID: " . $sessionDetails['id'] . "\n";
        echo "   Title: " . $sessionDetails['title'] . "\n";
        echo "   Links count: " . count($sessionDetails['links'] ?? []) . "\n";
        echo "   Notes count: " . count($sessionDetails['notes'] ?? []) . "\n";
        echo "   Plans count: " . count($sessionDetails['plans'] ?? []) . "\n";
    } else {
        echo "❌ getSessionWithDetails(1, 1) returned no data\n";
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}

echo "\n✅ getById method test complete!\n";
?>
