<?php
/**
 * Base Model Class
 *
 * Provides common functionality for all models.
 */

require_once __DIR__ . '/../utils/Database.php';
require_once __DIR__ . '/../utils/Cache.php';

abstract class BaseModel {
    protected $db;
    protected $cache;
    protected $table;
    protected $primaryKey = 'id';
    protected $cacheTTL = 3600; // Default cache TTL (1 hour)
    protected $useCache = true; // Whether to use caching by default

    public function __construct() {
        $this->db = Database::getInstance();
        $this->cache = Cache::getInstance();

        // Load app config to check if we're in production
        $appConfigPath = __DIR__ . '/../config/app.php';
        if (file_exists($appConfigPath)) {
            $appConfig = require_once $appConfigPath;
            $isProduction = isset($appConfig['environment']) && $appConfig['environment'] === 'production';

            // Only use cache in production by default
            $this->useCache = $isProduction && isset($appConfig['cache']['enabled']) ? $appConfig['cache']['enabled'] : false;

            // Set cache TTL from config if available
            if (isset($appConfig['cache']['ttl'])) {
                $this->cacheTTL = $appConfig['cache']['ttl'];
            }
        } else {
            // Default values if config file doesn't exist
            $this->useCache = false;
        }
    }

    /**
     * Find a record by ID
     *
     * @param int $id Record ID
     * @param bool $useCache Whether to use cache
     * @return array|false Record data or false if not found
     */
    public function find($id, $useCache = null) {
        // Use class default if not specified
        $useCache = $useCache !== null ? $useCache : $this->useCache;

        // Generate cache key
        $cacheKey = "model_{$this->table}_find_{$id}";

        // Try to get from cache first
        if ($useCache && $this->cache->has($cacheKey)) {
            return $this->cache->get($cacheKey);
        }

        // Get from database
        $sql = "SELECT * FROM {$this->table} WHERE {$this->primaryKey} = ? LIMIT 1";
        $result = $this->db->fetchOne($sql, [$id], $useCache);

        // Store in cache if found
        if ($result && $useCache) {
            $this->cache->set($cacheKey, $result, $this->cacheTTL);
        }

        return $result;
    }

    /**
     * Get a record by ID (alias for find method)
     *
     * @param int $id Record ID
     * @param bool $useCache Whether to use cache
     * @return array|false Record data or false if not found
     */
    public function getById($id, $useCache = null) {
        return $this->find($id, $useCache);
    }

    /**
     * Get all records
     *
     * @param bool $useCache Whether to use cache
     * @return array|false Records or false on failure
     */
    public function all($useCache = null) {
        // Use class default if not specified
        $useCache = $useCache !== null ? $useCache : $this->useCache;

        // Generate cache key
        $cacheKey = "model_{$this->table}_all";

        // Try to get from cache first
        if ($useCache && $this->cache->has($cacheKey)) {
            return $this->cache->get($cacheKey);
        }

        // Get from database
        $sql = "SELECT * FROM {$this->table}";
        $result = $this->db->fetchAll($sql, [], $useCache);

        // Store in cache if found
        if ($result && $useCache) {
            $this->cache->set($cacheKey, $result, $this->cacheTTL);
        }

        return $result;
    }

    /**
     * Create a new record
     *
     * @param array $data Record data
     * @return int|false New record ID or false on failure
     */
    public function create($data) {
        // Get app config to check if we're in production
        $appConfig = require_once __DIR__ . '/../config/app.php';
        $isProduction = isset($appConfig['environment']) && $appConfig['environment'] === 'production';

        // Only log in development mode
        if (!$isProduction) {
            error_log("Creating record in table {$this->table}: " . print_r($data, true));
        }

        $result = $this->db->insert($this->table, $data);

        if (!$isProduction) {
            error_log("Insert result: " . ($result ? "Success (ID: $result)" : "Failed"));
        }

        // Clear cache for this table if insert was successful
        if ($result) {
            $this->clearModelCache();
        }

        return $result;
    }

    /**
     * Update a record
     *
     * @param int $id Record ID
     * @param array $data Record data
     * @return bool Success
     */
    public function update($id, $data) {
        $result = $this->db->update(
            $this->table,
            $data,
            "{$this->primaryKey} = ?",
            [$id]
        );

        // Clear cache for this table and specific record if update was successful
        if ($result) {
            $this->clearModelCache();
            $this->cache->forget("model_{$this->table}_find_{$id}");
        }

        return $result;
    }

    /**
     * Delete a record
     *
     * @param int $id Record ID
     * @return bool Success
     */
    public function delete($id) {
        $result = $this->db->delete(
            $this->table,
            "{$this->primaryKey} = ?",
            [$id]
        );

        // Clear cache for this table and specific record if delete was successful
        if ($result) {
            $this->clearModelCache();
            $this->cache->forget("model_{$this->table}_find_{$id}");
        }

        return $result;
    }

    /**
     * Find records by a specific field
     *
     * @param string $field Field name
     * @param mixed $value Field value
     * @param bool $useCache Whether to use cache
     * @return array|false Records or false on failure
     */
    public function findBy($field, $value, $useCache = null) {
        // Use class default if not specified
        $useCache = $useCache !== null ? $useCache : $this->useCache;

        // Generate cache key
        $cacheKey = "model_{$this->table}_findBy_{$field}_" . md5(serialize($value));

        // Try to get from cache first
        if ($useCache && $this->cache->has($cacheKey)) {
            return $this->cache->get($cacheKey);
        }

        // Get from database
        $sql = "SELECT * FROM {$this->table} WHERE {$field} = ?";
        $result = $this->db->fetchAll($sql, [$value], $useCache);

        // Store in cache if found
        if ($result && $useCache) {
            $this->cache->set($cacheKey, $result, $this->cacheTTL);
        }

        return $result;
    }

    /**
     * Find one record by a specific field
     *
     * @param string $field Field name
     * @param mixed $value Field value
     * @param bool $useCache Whether to use cache
     * @return array|false Record or false on failure
     */
    public function findOneBy($field, $value, $useCache = null) {
        // Use class default if not specified
        $useCache = $useCache !== null ? $useCache : $this->useCache;

        // Generate cache key
        $cacheKey = "model_{$this->table}_findOneBy_{$field}_" . md5(serialize($value));

        // Try to get from cache first
        if ($useCache && $this->cache->has($cacheKey)) {
            return $this->cache->get($cacheKey);
        }

        // Get from database
        $sql = "SELECT * FROM {$this->table} WHERE {$field} = ? LIMIT 1";
        $result = $this->db->fetchOne($sql, [$value], $useCache);

        // Store in cache if found
        if ($result && $useCache) {
            $this->cache->set($cacheKey, $result, $this->cacheTTL);
        }

        return $result;
    }

    /**
     * Count records
     *
     * @param bool $useCache Whether to use cache
     * @return int Record count
     */
    public function count($useCache = null) {
        // Use class default if not specified
        $useCache = $useCache !== null ? $useCache : $this->useCache;

        // Generate cache key
        $cacheKey = "model_{$this->table}_count";

        // Try to get from cache first
        if ($useCache && $this->cache->has($cacheKey)) {
            return $this->cache->get($cacheKey);
        }

        // Get from database
        $sql = "SELECT COUNT(*) as count FROM {$this->table}";
        $result = $this->db->fetchOne($sql, [], $useCache);
        $count = $result ? $result['count'] : 0;

        // Store in cache
        if ($useCache) {
            $this->cache->set($cacheKey, $count, $this->cacheTTL);
        }

        return $count;
    }

    /**
     * Execute a query (wrapper for database query method)
     *
     * @param string $sql SQL query
     * @param array $params Query parameters
     * @return bool|PDOStatement Success or statement
     */
    protected function execute($sql, $params = []) {
        return $this->db->query($sql, $params);
    }

    /**
     * Clear all cache for this model
     *
     * @return void
     */
    protected function clearModelCache() {
        // Clear common cache keys
        $this->cache->forget("model_{$this->table}_all");
        $this->cache->forget("model_{$this->table}_count");

        // Note: Individual record caches will be cleared separately
        // when those specific records are updated or deleted
    }
}
